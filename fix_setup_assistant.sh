#!/bin/bash

echo "🔧 Creating Setup Assistant completion files..."

# Kill Setup Assistant processes
sudo killall -9 "Setup Assistant" 2>/dev/null
sudo killall -9 "mbuseragent" 2>/dev/null
sudo killall -9 "mbsystemadministration" 2>/dev/null

# Set all Setup Assistant preferences to complete
echo "📝 Setting Setup Assistant preferences..."

# Core setup completion flags
defaults write com.apple.SetupAssistant DidSeeCloudSetup -bool true
defaults write com.apple.SetupAssistant DidSeePrivacy -bool true
defaults write com.apple.SetupAssistant DidSeeScreenTime -bool true
defaults write com.apple.SetupAssistant DidSeeAppleID -bool true
defaults write com.apple.SetupAssistant DidSeeiCloudLogin -bool true
defaults write com.apple.SetupAssistant DidSeeAnalytics -bool true
defaults write com.apple.SetupAssistant DidSeeSiriSetup -bool true
defaults write com.apple.SetupAssistant DidSeeFileVault -bool true
defaults write com.apple.SetupAssistant DidSeeTouchID -bool true
defaults write com.apple.SetupAssistant DidSeeActivationLock -bool true

# Additional setup screens
defaults write com.apple.SetupAssistant DidSeeAppStore -bool true
defaults write com.apple.SetupAssistant DidSeeApplePaySetup -bool true
defaults write com.apple.SetupAssistant DidSeeLockdownMode -bool true
defaults write com.apple.SetupAssistant DidSeeSyncSetup -bool true
defaults write com.apple.SetupAssistant DidSeeSyncSetup2 -bool true
defaults write com.apple.SetupAssistant DidSeeTermsOfAddress -bool true
defaults write com.apple.SetupAssistant DidSeeTouchIDSetup -bool true
defaults write com.apple.SetupAssistant DidSeeMigrationIntro -bool true
defaults write com.apple.SetupAssistant DidSeeTransferInfo -bool true
defaults write com.apple.SetupAssistant DidSeeAvatarSetup -bool true
defaults write com.apple.SetupAssistant DidSeeAppearanceSetup -bool true
defaults write com.apple.SetupAssistant DidSeeTrueTonePrivacy -bool true
defaults write com.apple.SetupAssistant DidSeeAccessibility -bool true

# Mini Buddy control flags
defaults write com.apple.SetupAssistant MiniBuddyShouldLaunchToResumeSetup -bool false
defaults write com.apple.SetupAssistant MiniBuddyLaunchedPostMigration -bool true
defaults write com.apple.SetupAssistant SkipExpressSettingsUpdating -bool true
defaults write com.apple.SetupAssistant RunNonInteractive -bool true
defaults write com.apple.SetupAssistant SkipFirstLoginOptimization -bool true

# Version tracking
defaults write com.apple.SetupAssistant GestureMovieSeen none
defaults write com.apple.SetupAssistant LastSeenCloudProductVersion $(sw_vers -productVersion)
defaults write com.apple.SetupAssistant LastSeenBuddyBuildVersion $(sw_vers -buildVersion)

echo "📁 Creating system completion files..."

# Create all possible setup completion files
sudo touch /var/db/.AppleSetupDone 2>/dev/null || echo "Could not create .AppleSetupDone"
sudo touch /var/db/.AppleDiagnosticsSetupDone 2>/dev/null || echo "Could not create .AppleDiagnosticsSetupDone"
sudo touch /var/db/.AppleLanguageSetupDone 2>/dev/null || echo "Could not create .AppleLanguageSetupDone"

# Try to create MDM-related completion files
sudo touch /var/db/.AppleMDMSetupDone 2>/dev/null || echo "Could not create .AppleMDMSetupDone"
sudo touch /var/db/.AppleCloudSetupDone 2>/dev/null || echo "Could not create .AppleCloudSetupDone"
sudo touch /var/db/.AppleEnrollmentSetupDone 2>/dev/null || echo "Could not create .AppleEnrollmentSetupDone"

# Create user-level completion files
touch ~/.SetupAssistantCompleted 2>/dev/null || echo "Could not create user completion file"

echo "🔧 Setting system-level preferences..."

# System-level preferences
sudo defaults write /Library/Preferences/com.apple.SetupAssistant DidSeeCloudSetup -bool true 2>/dev/null || echo "Could not set system DidSeeCloudSetup"
sudo defaults write /Library/Preferences/com.apple.SetupAssistant SkipCloudSetup -bool true 2>/dev/null || echo "Could not set system SkipCloudSetup"
sudo defaults write /Library/Preferences/com.apple.SetupAssistant SkipSiriSetup -bool true 2>/dev/null || echo "Could not set system SkipSiriSetup"
sudo defaults write /Library/Preferences/com.apple.SetupAssistant SkipPrivacySetup -bool true 2>/dev/null || echo "Could not set system SkipPrivacySetup"

echo "🛑 Final kill of Setup Assistant processes..."

# Final kill
sudo killall -9 "Setup Assistant" 2>/dev/null
sudo killall -9 "mbuseragent" 2>/dev/null
sudo killall -9 "mbsystemadministration" 2>/dev/null

echo "✅ Setup Assistant should now be disabled!"
echo "🔍 Checking if Setup Assistant is still running..."

sleep 2

if pgrep -f "Setup Assistant" > /dev/null; then
    echo "⚠️ Setup Assistant is still running. This may require a reboot or SIP modification."
    echo "💡 Try logging out and back in, or restarting your Mac."
else
    echo "🎉 Success! Setup Assistant is no longer running."
fi
