# Environment variables
.env
.env.*
!.env.example

# Wallet and keys
keys/
*.wallet
*.keypair
*_keypair.json
jito_shredstream_keypair.json

# Python
__pycache__/
*.py[cod]
*$py.class
*.so
.Python
env/
build/
develop-eggs/
dist/
downloads/
eggs/
.eggs/
lib/
lib64/
parts/
sdist/
var/
*.egg-info/
.installed.cfg
*.egg

# Virtual environments
venv/
ENV/
env/

# IDE files
.idea/
.vscode/
*.swp
*.swo
.DS_Store

# Logs and outputs
logs/
*.log
output/
*.out
*.err

# Jupyter Notebooks
.ipynb_checkpoints

# Docker
.dockerignore
docker-compose.override.yml

# Database
*.db
*.sqlite3

# Temporary files
*.tmp
*.bak
*.swp
*~

# OS generated files
.DS_Store
.DS_Store?
._*
.Spotlight-V100
.Trashes
ehthumbs.db
Thumbs.db
