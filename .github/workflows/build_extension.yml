name: Build PyO3 Extension

on:
  push:
    branches: [ main ]
    paths:
      - 'solana_tx_utils/**'
      - '.github/workflows/build_extension.yml'
  pull_request:
    branches: [ main ]
    paths:
      - 'solana_tx_utils/**'

jobs:
  build:
    runs-on: ${{ matrix.os }}
    strategy:
      fail-fast: false
      matrix:
        os: [ubuntu-latest, macos-latest]
        python-version: ['3.9']

    steps:
    - uses: actions/checkout@v3
    
    - name: Set up Python ${{ matrix.python-version }}
      uses: actions/setup-python@v4
      with:
        python-version: ${{ matrix.python-version }}
    
    - name: Install Rust
      uses: actions-rs/toolchain@v1
      with:
        toolchain: stable
        profile: minimal
        override: true
    
    - name: Install dependencies
      run: |
        python -m pip install --upgrade pip
        pip install maturin pytest
        pip install -r requirements.txt
    
    - name: Build and install extension
      run: |
        cd solana_tx_utils
        maturin build --release
        pip install --force-reinstall target/wheels/solana_tx_utils*.whl
    
    - name: Test with pytest
      run: |
        cd integration_tests
        pytest -xvs tx_utils_tests/
    
    - name: Upload wheels
      uses: actions/upload-artifact@v3
      with:
        name: wheels-${{ matrix.os }}-${{ matrix.python-version }}
        path: solana_tx_utils/target/wheels/*.whl
