#!/bin/bash
# Synergy7 Trading System Environment Activation Script
# This script activates the virtual environment and sets up the environment

echo "🚀 Activating Synergy7 Trading System Environment..."

# Check if we're in the right directory
if [ ! -f "requirements.txt" ]; then
    echo "❌ Error: Please run this script from the Synergy7 project root directory"
    exit 1
fi

# Check if virtual environment exists
if [ ! -d "synergy7_env" ]; then
    echo "❌ Error: Virtual environment 'synergy7_env' not found"
    echo "Please run: python3 -m venv synergy7_env && pip install -r requirements.txt"
    exit 1
fi

# Activate virtual environment
source synergy7_env/bin/activate

# Verify activation
if [ "$VIRTUAL_ENV" != "" ]; then
    echo "✅ Virtual environment activated: $VIRTUAL_ENV"
else
    echo "❌ Failed to activate virtual environment"
    exit 1
fi

# Check key dependencies
echo "🔍 Checking key dependencies..."
python -c "
import sys
try:
    import solders
    import rich
    import pandas
    import numpy
    import streamlit
    import solana
    print('✅ All key packages available!')
    print(f'Python: {sys.version.split()[0]}')
    print(f'Solders: {solders.__version__}')
    print(f'Rich: {rich.__version__}')
    print(f'Pandas: {pandas.__version__}')
    print(f'Numpy: {numpy.__version__}')
except ImportError as e:
    print(f'❌ Import error: {e}')
    sys.exit(1)
"

if [ $? -eq 0 ]; then
    echo ""
    echo "🎉 Synergy7 Trading System environment is ready!"
    echo ""
    echo "Available commands:"
    echo "  python phase_4_deployment/check_dependencies.py  - Check all dependencies"
    echo "  python test_imports.py                          - Test critical imports"
    echo "  streamlit run [dashboard_file]                  - Run Streamlit dashboard"
    echo ""
    echo "To deactivate: deactivate"
else
    echo "❌ Environment setup failed"
    exit 1
fi
