# ================================================================
# SYNERGY7 TRADING SYSTEM - ENVIRONMENT CONFIGURATION
# ================================================================
# Copy this file to .env and fill in your actual API keys and settings
# NEVER commit the actual .env file to version control!

# ================================================================
# 🔑 WALLET CONFIGURATION (REQUIRED)
# ================================================================
# Your Solana wallet address (public key)
WALLET_ADDRESS=your_wallet_address_here

# Your Solana wallet private key (base58 encoded)
# ⚠️ KEEP THIS SECURE! Never share or commit this key
WALLET_PRIVATE_KEY=your_base58_private_key_here

# Optional: Path to keypair file (alternative to WALLET_PRIVATE_KEY)
KEYPAIR_PATH=keys/wallet.json

# ================================================================
# 🌐 SOLANA RPC ENDPOINTS (REQUIRED)
# ================================================================
# Primary Helius RPC endpoint (recommended for best performance)
HELIUS_API_KEY=your_helius_api_key_here
HELIUS_RPC_URL=https://mainnet.helius-rpc.com/?api-key=${HELIUS_API_KEY}

# QuickNode RPC endpoint (premium service)
QUICKNODE_API_KEY=your_quicknode_api_key_here
QUICKNODE_RPC_URL=https://your-endpoint.solana-mainnet.quiknode.pro/your_token/

# Fallback RPC endpoints
FALLBACK_RPC_URL=https://api.mainnet-beta.solana.com
JITO_RPC_URL=https://mainnet.block-engine.jito.wtf/api/v1

# ================================================================
# 📊 MARKET DATA APIs (REQUIRED)
# ================================================================
# Birdeye API for token prices and market data
BIRDEYE_API_KEY=your_birdeye_api_key_here

# CoinGecko API for additional price data
COINGECKO_API_KEY=your_coingecko_api_key_here

# Jupiter API for DEX aggregation
JUPITER_API_URL=https://quote-api.jup.ag/v6

# ================================================================
# 🤖 AI INTEGRATION (OPTIONAL)
# ================================================================
# X.AI (Grok) API for AI-powered analysis
XAI_API_KEY=your_xai_api_key_here
XAI_MODEL=grok-3-latest
XAI_BASE_URL=https://api.x.ai/v1

# ================================================================
# 📱 TELEGRAM NOTIFICATIONS (REQUIRED)
# ================================================================
# Telegram bot token for notifications
TELEGRAM_BOT_TOKEN=your_telegram_bot_token_here

# Your Telegram chat ID for receiving notifications
TELEGRAM_CHAT_ID=your_telegram_chat_id_here

# ================================================================
# 🚀 ADVANCED RPC SERVICES (OPTIONAL)
# ================================================================
# QuickNode Bundle API for MEV protection
QUICKNODE_BUNDLE_URL=https://api.quicknode.com/v1/solana/mainnet/bundles
QUICKNODE_BUNDLES_ENABLED=true
QUICKNODE_MAX_BUNDLE_SIZE=5
QUICKNODE_BUNDLE_TIMEOUT=30
QUICKNODE_PRIORITY_FEE=20000
QUICKNODE_RETRY_ATTEMPTS=3
QUICKNODE_FALLBACK_JITO=true

# QuickNode Price Feeds
QUICKNODE_PRICE_API_URL=https://api.quicknode.com/v1/solana/mainnet/prices
QUICKNODE_PRICE_FEEDS_ENABLED=true
QUICKNODE_PRICE_CACHE=30
QUICKNODE_PRICE_FALLBACK_COINGECKO=true
QUICKNODE_PRICE_FALLBACK_JUPITER=true
QUICKNODE_PRICE_RETRIES=3
QUICKNODE_PRICE_TIMEOUT=10

# QuickNode Streaming (Yellowstone)
QUICKNODE_STREAMING_ENABLED=true
QUICKNODE_GRPC_ENDPOINT=grpc.solana.com:443
QUICKNODE_STREAM_BUFFER=1000
QUICKNODE_HEARTBEAT=30
QUICKNODE_MAX_RECONNECTS=10
QUICKNODE_RECONNECT_DELAY=5
QUICKNODE_STREAM_ACCOUNTS=true
QUICKNODE_STREAM_TRANSACTIONS=true
QUICKNODE_STREAM_BLOCKS=false
QUICKNODE_STREAM_SLOTS=false

# ================================================================
# ⚙️ TRADING CONFIGURATION
# ================================================================
# Trading mode settings
TRADING_MODE=live
PAPER_TRADING=false
DRY_RUN=false

# Risk management
MAX_POSITION_SIZE=0.8
STOP_LOSS_PCT=0.034
TAKE_PROFIT_PCT=0.092
TRAILING_STOP_PCT=0.016

# Transaction settings
SLIPPAGE_TOLERANCE=0.01
MAX_TRANSACTION_FEE=0.01
JUPITER_AUTO_SLIPPAGE=true
JUPITER_SLIPPAGE_BPS=50
JUPITER_MAX_ACCOUNTS=20

# Timing configuration
TRADING_CYCLE_INTERVAL_SECONDS=60
MIN_TRADE_INTERVAL=300
UPDATE_INTERVAL=10

# ================================================================
# 🔧 SYSTEM CONFIGURATION
# ================================================================
# Logging level
LOG_LEVEL=INFO

# Circuit breaker settings
CIRCUIT_BREAKER_ENABLED=true
CIRCUIT_BREAKER_THRESHOLD=3
CIRCUIT_BREAKER_RESET=60

# Risk management
RISK_ADJUSTMENT_FACTOR=0.5
RISK_WEIGHT=0.3
SHARPE_THRESHOLD=1.0
WEIGHT_UPDATE_INTERVAL=3600

# ================================================================
# 🐋 WHALE WATCHING (OPTIONAL)
# ================================================================
WHALE_WATCHING_ENABLED=true
WHALE_MIN_TRANSACTION=100000
WHALE_LOOKBACK_HOURS=24
WHALE_CONFIDENCE_WEIGHT=0.3
WHALE_DISCOVERY_INTERVAL=3600
WHALE_SIGNAL_DECAY=6

# ================================================================
# 📈 MONITORING & ALERTS (OPTIONAL)
# ================================================================
# Prometheus metrics
PROMETHEUS_ENABLED=true
PROMETHEUS_PORT=8000

# Grafana dashboard
GRAFANA_ADMIN_PASSWORD=admin

# Alert thresholds
EXPOSURE_ALERT_THRESHOLD=0.4
LOSS_ALERT_THRESHOLD=0.02
PROFIT_ALERT_THRESHOLD=0.01

# ================================================================
# 🗄️ DATABASE (OPTIONAL)
# ================================================================
# Database connection string (if using database storage)
DB_CONNECTION_STRING=sqlite:///data/trading.db

# ================================================================
# 🔐 SECURITY (OPTIONAL)
# ================================================================
# API rate limiting
API_RATE_LIMIT_PER_MINUTE=100
API_RETRY_DELAY=2.0
API_MAX_RETRIES=5
API_TIMEOUT=10

# ================================================================
# 🧪 TESTING & DEVELOPMENT
# ================================================================
# Test configuration
TEST_MARKET=devnet
TEST_SIZE=0.01
MIN_TEST_LAMPORTS=1000

# Development settings
DEBUG_MODE=false
VERBOSE_LOGGING=false

# ================================================================
# 📦 DEPLOYMENT (OPTIONAL)
# ================================================================
# Docker configuration
CONTAINER_NAME=synergy7_trading_bot
IMAGE_TAG=latest
RESTART_POLICY=unless-stopped

# Streamlit dashboard
STREAMLIT_PORT=8501
STREAMLIT_HEADLESS=false

# Health check
HEALTH_CHECK_PORT=8080
HEALTH_CHECK_ENABLED=true

# ================================================================
# 🎯 QUANTCONNECT INTEGRATION (OPTIONAL)
# ================================================================
# QuantConnect API (if using cloud backtesting)
QUANTCONNECT_PROJECT_ID=your_project_id
QUANTCONNECT_BACKTEST_ID=your_backtest_id
Q5_API_KEY=your_q5_api_key

# ================================================================
# 📝 NOTES
# ================================================================
# 1. Replace all "your_*_here" values with actual credentials
# 2. Keep your .env file secure and never commit it to version control
# 3. Use strong, unique API keys for all services
# 4. Test with small amounts first before going live
# 5. Monitor your API usage to avoid rate limits
# 6. Regularly rotate your API keys for security
