#!/usr/bin/env python3
"""
Environment Configuration Validator for Synergy7 Trading System
Validates all API keys and configuration settings in .env file.
"""

import os
import sys
import asyncio
import httpx
from pathlib import Path
from dotenv import load_dotenv
from rich.console import Console
from rich.table import Table
from rich.panel import Panel
from rich import print as rprint

# Add project root to path
project_root = Path(__file__).parent.parent
sys.path.append(str(project_root))

console = Console()

class EnvValidator:
    """Validates environment configuration."""
    
    def __init__(self):
        self.results = {}
        self.required_vars = [
            'WALLET_ADDRESS',
            'WALLET_PRIVATE_KEY', 
            'HELIUS_API_KEY',
            'TELEGRAM_BOT_TOKEN',
            'TELEGRAM_CHAT_ID',
            'BIRDEYE_API_KEY'
        ]
        self.optional_vars = [
            'QUICKNODE_API_KEY',
            'COINGECKO_API_KEY',
            'JITO_RPC_URL'
        ]
    
    def load_env(self):
        """Load environment variables."""
        env_path = project_root / '.env'
        if not env_path.exists():
            console.print("❌ .env file not found! Please copy .env.example to .env", style="red")
            return False
        
        load_dotenv(env_path)
        console.print("✅ .env file loaded", style="green")
        return True
    
    def check_required_vars(self):
        """Check required environment variables."""
        console.print("\n🔍 Checking Required Variables", style="bold blue")
        
        missing_vars = []
        for var in self.required_vars:
            value = os.getenv(var)
            if value and value != f"your_{var.lower()}_here":
                console.print(f"✅ {var}: Set", style="green")
                self.results[var] = {'status': 'OK', 'value': value[:10] + '...' if len(value) > 10 else value}
            else:
                console.print(f"❌ {var}: Missing or placeholder", style="red")
                missing_vars.append(var)
                self.results[var] = {'status': 'MISSING', 'value': None}
        
        return len(missing_vars) == 0
    
    def check_optional_vars(self):
        """Check optional environment variables."""
        console.print("\n🔍 Checking Optional Variables", style="bold blue")
        
        for var in self.optional_vars:
            value = os.getenv(var)
            if value and value != f"your_{var.lower()}_here":
                console.print(f"✅ {var}: Set", style="green")
                self.results[var] = {'status': 'OK', 'value': value[:10] + '...' if len(value) > 10 else value}
            else:
                console.print(f"⚠️ {var}: Not set (optional)", style="yellow")
                self.results[var] = {'status': 'OPTIONAL', 'value': None}
    
    def validate_wallet_format(self):
        """Validate wallet address and private key format."""
        console.print("\n🔍 Validating Wallet Format", style="bold blue")
        
        wallet_address = os.getenv('WALLET_ADDRESS')
        wallet_private_key = os.getenv('WALLET_PRIVATE_KEY')
        
        # Validate wallet address (should be 32-44 characters, base58)
        if wallet_address:
            if len(wallet_address) >= 32 and len(wallet_address) <= 44:
                console.print("✅ Wallet address format looks valid", style="green")
            else:
                console.print("❌ Wallet address format invalid (should be 32-44 characters)", style="red")
                return False
        
        # Validate private key (should be 64-88 characters, base58)
        if wallet_private_key:
            if len(wallet_private_key) >= 64 and len(wallet_private_key) <= 88:
                console.print("✅ Private key format looks valid", style="green")
            else:
                console.print("❌ Private key format invalid (should be 64-88 characters)", style="red")
                return False
        
        return True
    
    async def test_api_connections(self):
        """Test API connections."""
        console.print("\n🔍 Testing API Connections", style="bold blue")
        
        async with httpx.AsyncClient(timeout=10.0) as client:
            # Test Helius API
            await self._test_helius_api(client)
            
            # Test Birdeye API
            await self._test_birdeye_api(client)
            
            # Test Telegram Bot
            await self._test_telegram_bot(client)
            
            # Test QuickNode (if configured)
            await self._test_quicknode_api(client)
            
            # Test CoinGecko (if configured)
            await self._test_coingecko_api(client)
    
    async def _test_helius_api(self, client):
        """Test Helius API connection."""
        api_key = os.getenv('HELIUS_API_KEY')
        if not api_key:
            return
        
        try:
            url = f"https://mainnet.helius-rpc.com/?api-key={api_key}"
            payload = {
                "jsonrpc": "2.0",
                "id": 1,
                "method": "getHealth"
            }
            
            response = await client.post(url, json=payload)
            if response.status_code == 200:
                console.print("✅ Helius API: Connected", style="green")
            else:
                console.print(f"❌ Helius API: Error {response.status_code}", style="red")
        except Exception as e:
            console.print(f"❌ Helius API: Connection failed - {str(e)}", style="red")
    
    async def _test_birdeye_api(self, client):
        """Test Birdeye API connection."""
        api_key = os.getenv('BIRDEYE_API_KEY')
        if not api_key:
            return
        
        try:
            url = "https://public-api.birdeye.so/defi/tokenlist"
            headers = {"X-API-KEY": api_key}
            
            response = await client.get(url, headers=headers)
            if response.status_code == 200:
                console.print("✅ Birdeye API: Connected", style="green")
            else:
                console.print(f"❌ Birdeye API: Error {response.status_code}", style="red")
        except Exception as e:
            console.print(f"❌ Birdeye API: Connection failed - {str(e)}", style="red")
    
    async def _test_telegram_bot(self, client):
        """Test Telegram Bot connection."""
        bot_token = os.getenv('TELEGRAM_BOT_TOKEN')
        if not bot_token:
            return
        
        try:
            url = f"https://api.telegram.org/bot{bot_token}/getMe"
            
            response = await client.get(url)
            if response.status_code == 200:
                data = response.json()
                if data.get('ok'):
                    bot_name = data.get('result', {}).get('username', 'Unknown')
                    console.print(f"✅ Telegram Bot: Connected (@{bot_name})", style="green")
                else:
                    console.print("❌ Telegram Bot: Invalid token", style="red")
            else:
                console.print(f"❌ Telegram Bot: Error {response.status_code}", style="red")
        except Exception as e:
            console.print(f"❌ Telegram Bot: Connection failed - {str(e)}", style="red")
    
    async def _test_quicknode_api(self, client):
        """Test QuickNode API connection."""
        rpc_url = os.getenv('QUICKNODE_RPC_URL')
        if not rpc_url:
            console.print("⚠️ QuickNode API: Not configured (optional)", style="yellow")
            return
        
        try:
            payload = {
                "jsonrpc": "2.0",
                "id": 1,
                "method": "getHealth"
            }
            
            response = await client.post(rpc_url, json=payload)
            if response.status_code == 200:
                console.print("✅ QuickNode API: Connected", style="green")
            else:
                console.print(f"❌ QuickNode API: Error {response.status_code}", style="red")
        except Exception as e:
            console.print(f"❌ QuickNode API: Connection failed - {str(e)}", style="red")
    
    async def _test_coingecko_api(self, client):
        """Test CoinGecko API connection."""
        api_key = os.getenv('COINGECKO_API_KEY')
        if not api_key:
            console.print("⚠️ CoinGecko API: Not configured (optional)", style="yellow")
            return
        
        try:
            url = "https://api.coingecko.com/api/v3/ping"
            headers = {"x-cg-demo-api-key": api_key} if api_key else {}
            
            response = await client.get(url, headers=headers)
            if response.status_code == 200:
                console.print("✅ CoinGecko API: Connected", style="green")
            else:
                console.print(f"❌ CoinGecko API: Error {response.status_code}", style="red")
        except Exception as e:
            console.print(f"❌ CoinGecko API: Connection failed - {str(e)}", style="red")
    
    def generate_report(self):
        """Generate validation report."""
        console.print("\n📊 Validation Report", style="bold blue")
        
        table = Table(title="Environment Configuration Status")
        table.add_column("Variable", style="cyan")
        table.add_column("Status", style="magenta")
        table.add_column("Value", style="green")
        
        for var, result in self.results.items():
            status = result['status']
            value = result['value'] or 'Not Set'
            
            if status == 'OK':
                status_display = "✅ OK"
            elif status == 'MISSING':
                status_display = "❌ MISSING"
            else:
                status_display = "⚠️ OPTIONAL"
            
            table.add_row(var, status_display, value)
        
        console.print(table)
    
    def show_next_steps(self):
        """Show next steps based on validation results."""
        missing_required = [var for var in self.required_vars if self.results.get(var, {}).get('status') == 'MISSING']
        
        if missing_required:
            console.print("\n❌ Configuration Incomplete", style="bold red")
            console.print("\nMissing required variables:")
            for var in missing_required:
                console.print(f"  • {var}", style="red")
            
            console.print("\n📋 Next Steps:")
            console.print("1. Edit your .env file and add the missing variables")
            console.print("2. See docs/API_SETUP_GUIDE.md for help getting API keys")
            console.print("3. Run this script again to validate")
        else:
            console.print("\n✅ Configuration Complete!", style="bold green")
            console.print("\n🚀 Next Steps:")
            console.print("1. Run system tests: python system_tests/run_all_tests.py")
            console.print("2. Validate system: python scripts/validate_profitable_system.py")
            console.print("3. Start trading: python scripts/unified_live_trading.py")

async def main():
    """Main validation function."""
    console.print(Panel.fit("🔧 Synergy7 Trading System - Environment Validator", style="bold blue"))
    
    validator = EnvValidator()
    
    # Load environment
    if not validator.load_env():
        return
    
    # Check variables
    required_ok = validator.check_required_vars()
    validator.check_optional_vars()
    
    # Validate formats
    format_ok = validator.validate_wallet_format()
    
    # Test API connections (only if required vars are set)
    if required_ok and format_ok:
        await validator.test_api_connections()
    
    # Generate report
    validator.generate_report()
    validator.show_next_steps()

if __name__ == "__main__":
    asyncio.run(main())
