#!/bin/bash

echo "🔄 Starting continuous Setup Assistant killer..."
echo "Press Ctrl+C to stop"

while true; do
    # Kill Setup Assistant if it's running
    if pgrep -f "Setup Assistant" > /dev/null; then
        echo "⚠️ $(date): Setup Assistant detected - killing..."
        echo "zarilove" | sudo -S killall -9 "Setup Assistant" 2>/dev/null
        echo "zarilove" | sudo -S killall -9 "mbuseragent" 2>/dev/null
        echo "zarilove" | sudo -S killall -9 "mbsystemadministration" 2>/dev/null
    fi
    
    sleep 1
done
