# Synergy7 Trading System Requirements
# Comprehensive dependency list with careful versioning

# Core dependencies
httpx>=0.24.0
aiohttp>=3.8.4
solders>=0.20.0,<0.21.0  # Careful versioning for Solana compatibility
base58>=2.1.1
pyyaml>=6.0
python-dotenv>=1.0.0
pyzmq>=24.0.0
loguru>=0.7.0

# HTTP and networking
requests>=2.28.0
websockets>=11.0.0
tenacity>=8.2.0

# Data processing
pandas>=2.0.0
numpy>=1.24.0
scipy>=1.10.1
vectorbt>=0.24.0
ta>=0.10.0
scikit-learn>=1.0.0

# Visualization and UI
streamlit>=1.22.0
plotly>=5.14.0
matplotlib>=3.7.0
rich>=13.0.0  # For beautiful console output

# Solana dependencies
solana>=0.29.0
# solana-py>=0.29.0  # Not available for Python 3.9
anchorpy>=0.17.0

# API clients (optional - may not be available on PyPI)
# birdeye-sdk>=0.1.0
# helius-sdk>=0.1.0

# Monitoring and alerting
prometheus-client>=0.16.0
psutil>=5.9.0
python-telegram-bot>=13.0

# Utilities
tqdm>=4.65.0

# Development and testing
pytest>=7.3.1
pytest-asyncio>=0.21.0
pytest-cov>=3.0.0
black>=23.3.0
isort>=5.10.0
mypy>=1.3.0
flake8>=6.0.0

# PyO3 and Rust integration
maturin>=0.14.0

# Additional dependencies found in codebase
asyncio-mqtt>=0.11.0  # For MQTT communication (if used)
pydantic>=1.10.0      # For data validation (commonly used)
typing-extensions>=4.0.0  # For enhanced typing support
