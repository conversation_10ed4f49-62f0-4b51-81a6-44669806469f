#!/usr/bin/env python3
"""
Quick Environment Setup Script for Synergy7 Trading System
Helps users create their .env file with guided prompts.
"""

import os
import sys
from pathlib import Path
from rich.console import Console
from rich.prompt import Prompt, Confirm
from rich.panel import Panel
from rich import print as rprint

console = Console()

def main():
    """Main setup function."""
    console.print(Panel.fit("🚀 Synergy7 Trading System - Quick Setup", style="bold blue"))
    
    # Check if .env already exists
    env_path = Path('.env')
    if env_path.exists():
        if not Confirm.ask("⚠️ .env file already exists. Overwrite?"):
            console.print("Setup cancelled.", style="yellow")
            return
    
    console.print("\n📋 This script will help you create your .env configuration file.")
    console.print("You can skip optional fields by pressing Enter.\n")
    
    # Collect required information
    config = {}
    
    # Wallet Configuration
    console.print("🔑 [bold]Wallet Configuration[/bold]")
    config['WALLET_ADDRESS'] = Prompt.ask("Enter your Solana wallet address (public key)")
    config['WALLET_PRIVATE_KEY'] = Prompt.ask("Enter your Solana wallet private key (base58)", password=True)
    
    # RPC Endpoints
    console.print("\n🌐 [bold]RPC Endpoints[/bold]")
    config['HELIUS_API_KEY'] = Prompt.ask("Enter your Helius API key")
    
    quicknode_key = Prompt.ask("Enter your QuickNode API key (optional)", default="")
    if quicknode_key:
        config['QUICKNODE_API_KEY'] = quicknode_key
        config['QUICKNODE_RPC_URL'] = Prompt.ask("Enter your QuickNode RPC URL")
    
    # Market Data APIs
    console.print("\n📊 [bold]Market Data APIs[/bold]")
    config['BIRDEYE_API_KEY'] = Prompt.ask("Enter your Birdeye API key")
    
    coingecko_key = Prompt.ask("Enter your CoinGecko API key (optional)", default="")
    if coingecko_key:
        config['COINGECKO_API_KEY'] = coingecko_key
    
    # Telegram Notifications
    console.print("\n📱 [bold]Telegram Notifications[/bold]")
    config['TELEGRAM_BOT_TOKEN'] = Prompt.ask("Enter your Telegram bot token")
    config['TELEGRAM_CHAT_ID'] = Prompt.ask("Enter your Telegram chat ID")
    
    # Trading Configuration
    console.print("\n⚙️ [bold]Trading Configuration[/bold]")
    
    trading_mode = Prompt.ask(
        "Select trading mode",
        choices=["live", "paper", "dry_run"],
        default="dry_run"
    )
    
    if trading_mode == "live":
        config['TRADING_MODE'] = 'live'
        config['PAPER_TRADING'] = 'false'
        config['DRY_RUN'] = 'false'
        console.print("⚠️ [bold red]LIVE TRADING MODE SELECTED[/bold red]")
        console.print("Make sure you understand the risks and start with small amounts!")
    elif trading_mode == "paper":
        config['TRADING_MODE'] = 'paper'
        config['PAPER_TRADING'] = 'true'
        config['DRY_RUN'] = 'false'
    else:
        config['TRADING_MODE'] = 'dry_run'
        config['PAPER_TRADING'] = 'false'
        config['DRY_RUN'] = 'true'
    
    # Risk Management
    max_position = Prompt.ask("Maximum position size (0.1-1.0)", default="0.8")
    config['MAX_POSITION_SIZE'] = max_position
    
    slippage = Prompt.ask("Slippage tolerance (0.001-0.05)", default="0.01")
    config['SLIPPAGE_TOLERANCE'] = slippage
    
    # Create .env file
    create_env_file(config)
    
    # Show next steps
    show_next_steps()

def create_env_file(config):
    """Create the .env file with user configuration."""
    console.print("\n📝 Creating .env file...")
    
    env_content = """# ================================================================
# SYNERGY7 TRADING SYSTEM - ENVIRONMENT CONFIGURATION
# ================================================================
# Generated by setup script

# ================================================================
# 🔑 WALLET CONFIGURATION
# ================================================================
WALLET_ADDRESS={wallet_address}
WALLET_PRIVATE_KEY={wallet_private_key}

# ================================================================
# 🌐 SOLANA RPC ENDPOINTS
# ================================================================
HELIUS_API_KEY={helius_api_key}
HELIUS_RPC_URL=https://mainnet.helius-rpc.com/?api-key=${{HELIUS_API_KEY}}

{quicknode_config}

# Fallback endpoints
FALLBACK_RPC_URL=https://api.mainnet-beta.solana.com
JITO_RPC_URL=https://mainnet.block-engine.jito.wtf/api/v1

# ================================================================
# 📊 MARKET DATA APIs
# ================================================================
BIRDEYE_API_KEY={birdeye_api_key}

{coingecko_config}

# Jupiter API
JUPITER_API_URL=https://quote-api.jup.ag/v6

# ================================================================
# 📱 TELEGRAM NOTIFICATIONS
# ================================================================
TELEGRAM_BOT_TOKEN={telegram_bot_token}
TELEGRAM_CHAT_ID={telegram_chat_id}

# ================================================================
# ⚙️ TRADING CONFIGURATION
# ================================================================
TRADING_MODE={trading_mode}
PAPER_TRADING={paper_trading}
DRY_RUN={dry_run}

# Risk management
MAX_POSITION_SIZE={max_position_size}
SLIPPAGE_TOLERANCE={slippage_tolerance}
STOP_LOSS_PCT=0.034
TAKE_PROFIT_PCT=0.092

# Transaction settings
JUPITER_AUTO_SLIPPAGE=true
JUPITER_SLIPPAGE_BPS=50

# ================================================================
# 🔧 SYSTEM CONFIGURATION
# ================================================================
LOG_LEVEL=INFO
CIRCUIT_BREAKER_ENABLED=true
CIRCUIT_BREAKER_THRESHOLD=3
CIRCUIT_BREAKER_RESET=60

# ================================================================
# 🐋 WHALE WATCHING
# ================================================================
WHALE_WATCHING_ENABLED=true
WHALE_MIN_TRANSACTION=100000

# ================================================================
# 📈 MONITORING
# ================================================================
PROMETHEUS_ENABLED=true
EXPOSURE_ALERT_THRESHOLD=0.4
LOSS_ALERT_THRESHOLD=0.02
PROFIT_ALERT_THRESHOLD=0.01

# ================================================================
# 🧪 TESTING
# ================================================================
DEBUG_MODE=false
VERBOSE_LOGGING=false
""".format(
        wallet_address=config.get('WALLET_ADDRESS', ''),
        wallet_private_key=config.get('WALLET_PRIVATE_KEY', ''),
        helius_api_key=config.get('HELIUS_API_KEY', ''),
        quicknode_config=_format_quicknode_config(config),
        birdeye_api_key=config.get('BIRDEYE_API_KEY', ''),
        coingecko_config=_format_coingecko_config(config),
        telegram_bot_token=config.get('TELEGRAM_BOT_TOKEN', ''),
        telegram_chat_id=config.get('TELEGRAM_CHAT_ID', ''),
        trading_mode=config.get('TRADING_MODE', 'dry_run'),
        paper_trading=config.get('PAPER_TRADING', 'false'),
        dry_run=config.get('DRY_RUN', 'true'),
        max_position_size=config.get('MAX_POSITION_SIZE', '0.8'),
        slippage_tolerance=config.get('SLIPPAGE_TOLERANCE', '0.01')
    )
    
    with open('.env', 'w') as f:
        f.write(env_content)
    
    console.print("✅ .env file created successfully!", style="green")

def _format_quicknode_config(config):
    """Format QuickNode configuration section."""
    if 'QUICKNODE_API_KEY' in config:
        return f"""QUICKNODE_API_KEY={config['QUICKNODE_API_KEY']}
QUICKNODE_RPC_URL={config.get('QUICKNODE_RPC_URL', '')}
QUICKNODE_BUNDLES_ENABLED=true"""
    else:
        return """# QuickNode configuration (optional)
# QUICKNODE_API_KEY=your_quicknode_api_key_here
# QUICKNODE_RPC_URL=your_quicknode_rpc_url_here"""

def _format_coingecko_config(config):
    """Format CoinGecko configuration section."""
    if 'COINGECKO_API_KEY' in config:
        return f"COINGECKO_API_KEY={config['COINGECKO_API_KEY']}"
    else:
        return "# COINGECKO_API_KEY=your_coingecko_api_key_here"

def show_next_steps():
    """Show next steps after setup."""
    console.print("\n🎉 [bold green]Setup Complete![/bold green]")
    
    console.print("\n📋 [bold]Next Steps:[/bold]")
    console.print("1. Validate your configuration:")
    console.print("   [cyan]python scripts/validate_env_config.py[/cyan]")
    
    console.print("\n2. Run system tests:")
    console.print("   [cyan]python system_tests/run_all_tests.py[/cyan]")
    
    console.print("\n3. Validate the trading system:")
    console.print("   [cyan]python scripts/validate_profitable_system.py[/cyan]")
    
    console.print("\n4. Start trading (when ready):")
    console.print("   [cyan]python scripts/unified_live_trading.py[/cyan]")
    
    console.print("\n📚 [bold]Additional Resources:[/bold]")
    console.print("• API Setup Guide: [cyan]docs/API_SETUP_GUIDE.md[/cyan]")
    console.print("• System Documentation: [cyan]README.md[/cyan]")
    console.print("• Configuration Help: [cyan]docs/configuration_management.md[/cyan]")
    
    console.print("\n⚠️ [bold yellow]Important Reminders:[/bold yellow]")
    console.print("• Never share your private keys")
    console.print("• Start with small amounts for testing")
    console.print("• Monitor your trades closely")
    console.print("• Keep your API keys secure")

if __name__ == "__main__":
    main()
