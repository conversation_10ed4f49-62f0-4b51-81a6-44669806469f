#!/bin/bash

# Aggressive Setup Assistant Disabler
# This script continuously monitors and kills Setup Assistant

echo "🔧 Starting aggressive Setup Assistant disabler..."

# Function to kill Setup Assistant
kill_setup_assistant() {
    sudo killall -9 "Setup Assistant" 2>/dev/null
    sudo killall -9 "mbuseragent" 2>/dev/null
    killall -9 "Setup Assistant" 2>/dev/null
    killall -9 "mbuseragent" 2>/dev/null
}

# Set all possible Setup Assistant preferences
set_preferences() {
    # User preferences
    defaults write com.apple.SetupAssistant DidSeeCloudSetup -bool true
    defaults write com.apple.SetupAssistant DidSeePrivacy -bool true
    defaults write com.apple.SetupAssistant DidSeeScreenTime -bool true
    defaults write com.apple.SetupAssistant DidSeeAppleID -bool true
    defaults write com.apple.SetupAssistant DidSeeiCloudLogin -bool true
    defaults write com.apple.SetupAssistant DidSeeAnalytics -bool true
    defaults write com.apple.SetupAssistant DidSeeSiriSetup -bool true
    defaults write com.apple.SetupAssistant DidSeeFileVault -bool true
    defaults write com.apple.SetupAssistant DidSeeTouchID -bool true
    defaults write com.apple.SetupAssistant DidSeeActivationLock -bool true
    defaults write com.apple.SetupAssistant DidSeeMigrationIntro -bool true
    defaults write com.apple.SetupAssistant DidSeeTransferInfo -bool true
    defaults write com.apple.SetupAssistant DidSeeAvatarSetup -bool true
    defaults write com.apple.SetupAssistant DidSeeAppearanceSetup -bool true
    defaults write com.apple.SetupAssistant DidSeeTrueTonePrivacy -bool true
    defaults write com.apple.SetupAssistant DidSeeAccessibility -bool true
    defaults write com.apple.SetupAssistant RunNonInteractive -bool true
    defaults write com.apple.SetupAssistant SkipFirstLoginOptimization -bool true
    defaults write com.apple.SetupAssistant GestureMovieSeen none
    defaults write com.apple.SetupAssistant LastSeenCloudProductVersion $(sw_vers -productVersion)
    defaults write com.apple.SetupAssistant LastSeenBuddyBuildVersion $(sw_vers -buildVersion)
    
    # System preferences
    sudo defaults write /Library/Preferences/com.apple.SetupAssistant DidSeeCloudSetup -bool true 2>/dev/null
    sudo defaults write /Library/Preferences/com.apple.SetupAssistant SkipCloudSetup -bool true 2>/dev/null
    sudo defaults write /Library/Preferences/com.apple.SetupAssistant SkipSiriSetup -bool true 2>/dev/null
    sudo defaults write /Library/Preferences/com.apple.SetupAssistant SkipPrivacySetup -bool true 2>/dev/null
    
    # Create setup completion files
    sudo touch /var/db/.AppleSetupDone 2>/dev/null
    sudo touch /var/db/.AppleDiagnosticsSetupDone 2>/dev/null
    sudo touch /var/db/.AppleLanguageSetupDone 2>/dev/null
}

# Initial setup
set_preferences
kill_setup_assistant

echo "✅ Setup Assistant preferences set and processes killed"
echo "🔄 Monitoring for Setup Assistant processes..."

# Monitor and kill Setup Assistant continuously
while true; do
    if pgrep -f "Setup Assistant" > /dev/null; then
        echo "⚠️ Setup Assistant detected - killing..."
        kill_setup_assistant
        set_preferences
    fi
    sleep 2
done
