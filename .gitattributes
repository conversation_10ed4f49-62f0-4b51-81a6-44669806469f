# Auto detect text files and perform LF normalization
* text=auto

# Explicitly declare text files you want to always be normalized and converted
# to native line endings on checkout.
*.py text
*.rs text
*.js text
*.jsx text
*.ts text
*.tsx text
*.json text
*.md text
*.yml text
*.yaml text
*.toml text
*.sh text eol=lf

# Denote all files that are truly binary and should not be modified.
*.png binary
*.jpg binary
*.jpeg binary
*.gif binary
*.ico binary
*.whl binary
*.dylib binary
*.so binary
*.dll binary
