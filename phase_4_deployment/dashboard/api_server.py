#!/usr/bin/env python3
"""
API Server for Synergy7 Trading System Dashboard

This module provides an API server for the Synergy7 Trading System dashboard to fetch metrics.
"""

import os
import json
import time
import logging
import asyncio
import uvicorn
from fastapi import FastAPI, HTTPException
from fastapi.middleware.cors import CORSMiddleware
from typing import Dict, List, Any, Optional, Union

# Add parent directory to path
import sys
from pathlib import Path
sys.path.insert(0, str(Path(__file__).parent.parent.parent))

# Import monitoring service
try:
    from phase_4_deployment.monitoring.mock_monitoring_service import get_monitoring_service
except ImportError:
    # Try relative import
    sys.path.insert(0, str(Path(__file__).parent.parent))
    try:
        from monitoring.mock_monitoring_service import get_monitoring_service
    except ImportError:
        from monitoring.monitoring_service import get_monitoring_service

# Configure logging
logging.basicConfig(
    level=logging.INFO,
    format="%(asctime)s - %(name)s - %(levelname)s - %(message)s"
)
logger = logging.getLogger("api_server")

# Create FastAPI app
app = FastAPI(
    title="RWA Trading System API",
    description="API for RWA Trading System Dashboard",
    version="1.0.0"
)

# Add CORS middleware
app.add_middleware(
    CORSMiddleware,
    allow_origins=["*"],
    allow_credentials=True,
    allow_methods=["*"],
    allow_headers=["*"],
)

# Get monitoring service
monitoring = get_monitoring_service()

@app.get("/")
async def root():
    """Root endpoint."""
    return {"message": "RWA Trading System API"}

@app.get("/health")
async def health():
    """Health check endpoint."""
    # Run health checks
    health_results = monitoring.run_health_checks()

    # Calculate overall health
    overall_health = all(health_results.values())

    return {
        "overall_health": overall_health,
        "components": health_results,
        "timestamp": time.time()
    }

@app.get("/metrics")
async def metrics():
    """Metrics endpoint."""
    # Get metrics
    metrics = monitoring.get_metrics()

    return metrics

@app.get("/component/{component}")
async def component(component: str):
    """
    Component status endpoint.

    Args:
        component: Component name
    """
    # Get metrics
    metrics = monitoring.get_metrics()

    # Get component status
    component_status = metrics.get("component_status", {}).get(component)

    if component_status:
        return component_status
    else:
        raise HTTPException(status_code=404, detail=f"Component {component} not found")

@app.get("/market/{market}")
async def market(market: str):
    """
    Market microstructure endpoint.

    Args:
        market: Market identifier (e.g., "SOL-USDC")
    """
    # Get metrics
    metrics = monitoring.get_metrics()

    # Get market microstructure
    market_microstructure = metrics.get("market_microstructure", {}).get(market)

    if market_microstructure:
        return market_microstructure
    else:
        raise HTTPException(status_code=404, detail=f"Market {market} not found")

@app.get("/signal/{signal_type}")
async def signal(signal_type: str):
    """
    Statistical signal endpoint.

    Args:
        signal_type: Signal type (e.g., "price_momentum")
    """
    # Get metrics
    metrics = monitoring.get_metrics()

    # Get statistical signal
    statistical_signal = metrics.get("statistical_signals", {}).get(signal_type)

    if statistical_signal:
        return statistical_signal
    else:
        raise HTTPException(status_code=404, detail=f"Signal {signal_type} not found")

@app.get("/strategy-accuracy/{metric_type}")
async def strategy_accuracy(metric_type: str):
    """
    Strategy accuracy metrics endpoint.

    Args:
        metric_type: Metric type (e.g., "signal_generation", "directional_accuracy")
    """
    # Get metrics
    metrics = monitoring.get_metrics()

    # Get strategy accuracy metrics
    strategy_accuracy = metrics.get("strategy_accuracy", {}).get(metric_type)

    if strategy_accuracy:
        return strategy_accuracy
    else:
        raise HTTPException(status_code=404, detail=f"Strategy accuracy metric {metric_type} not found")

@app.get("/profit-metrics/{metric_type}")
async def profit_metrics(metric_type: str):
    """
    Profit metrics endpoint.

    Args:
        metric_type: Metric type (e.g., "net_profit", "profit_factor")
    """
    # Get metrics
    metrics = monitoring.get_metrics()

    # Get profit metrics
    profit_metrics = metrics.get("profit_metrics", {}).get(metric_type)

    if profit_metrics:
        return profit_metrics
    else:
        raise HTTPException(status_code=404, detail=f"Profit metric {metric_type} not found")

@app.get("/execution-quality/{metric_type}")
async def execution_quality(metric_type: str):
    """
    Execution quality metrics endpoint.

    Args:
        metric_type: Metric type (e.g., "slippage", "fill_rate")
    """
    # Get metrics
    metrics = monitoring.get_metrics()

    # Get execution quality metrics
    execution_quality = metrics.get("execution_quality", {}).get(metric_type)

    if execution_quality:
        return execution_quality
    else:
        raise HTTPException(status_code=404, detail=f"Execution quality metric {metric_type} not found")

@app.get("/wallet/{wallet_address}")
async def wallet(wallet_address: str):
    """
    Wallet balance endpoint.

    Args:
        wallet_address: Wallet address
    """
    # Get metrics
    metrics = monitoring.get_metrics()

    # Get wallet balance
    wallet_balance = metrics.get("wallet_balances", {}).get(wallet_address)

    if wallet_balance:
        return wallet_balance
    else:
        raise HTTPException(status_code=404, detail=f"Wallet {wallet_address} not found")

@app.get("/transaction/{signature}")
async def transaction(signature: str):
    """
    Transaction endpoint.

    Args:
        signature: Transaction signature
    """
    # Get metrics
    metrics = monitoring.get_metrics()

    # Get transaction
    transaction = metrics.get("transactions", {}).get(signature)

    if transaction:
        return transaction
    else:
        raise HTTPException(status_code=404, detail=f"Transaction {signature} not found")

def start_api_server(host: str = "0.0.0.0", port: int = 8081):
    """
    Start the API server.

    Args:
        host: Host to bind to
        port: Port to bind to
    """
    # Start monitoring service
    monitoring.start()

    # Start API server
    uvicorn.run(app, host=host, port=port)

if __name__ == "__main__":
    import argparse

    # Parse command line arguments
    parser = argparse.ArgumentParser(description="API Server for Synergy7 Trading System Dashboard")
    parser.add_argument("--host", type=str, default="0.0.0.0", help="Host to bind to")
    parser.add_argument("--port", type=int, default=8081, help="Port to bind to")
    args = parser.parse_args()

    # Start API server
    start_api_server(host=args.host, port=args.port)
