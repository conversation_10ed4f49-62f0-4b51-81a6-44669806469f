# Q5 Trading System Deployment Requirements

# Core dependencies
numpy>=1.23.0
pandas>=1.5.0
pyyaml>=6.0
python-dotenv>=1.0.0
pyzmq>=24.0.0
httpx>=0.24.0
asyncio>=3.4.3
solders>=0.18.0
streamlit>=1.22.0

# Monitoring and alerting
psutil>=5.9.0
python-telegram-bot>=13.0
prometheus-client>=0.16.0

# Visualization
matplotlib>=3.7.0
plotly>=5.14.0

# Utilities
tqdm>=4.65.0
loguru>=0.7.0
tenacity>=8.2.0
aiohttp>=3.8.4
websockets>=11.0.0
base58>=2.1.0

# Development tools
pytest>=7.3.1
black>=23.3.0
flake8>=6.0.0
mypy>=1.3.0
