#!/usr/bin/env python3
"""
Environment Setup Tests for Synergy7 Trading System
Tests all critical dependencies and environment configuration.
"""

import sys
import os
import importlib
import subprocess
from pathlib import Path
import pytest

class TestEnvironmentSetup:
    """Test environment setup and dependencies."""
    
    def test_python_version(self):
        """Test Python version compatibility."""
        version = sys.version_info
        assert version.major == 3, f"Python 3 required, got {version.major}"
        assert version.minor >= 8, f"Python 3.8+ required, got {version.major}.{version.minor}"
        print(f"✅ Python version: {version.major}.{version.minor}.{version.micro}")
    
    def test_virtual_environment(self):
        """Test virtual environment is active."""
        venv = os.environ.get('VIRTUAL_ENV')
        assert venv is not None, "Virtual environment not activated"
        assert 'synergy7_env' in venv, f"Wrong virtual environment: {venv}"
        print(f"✅ Virtual environment: {venv}")
    
    def test_core_dependencies(self):
        """Test core trading system dependencies."""
        core_deps = [
            'solders',
            'solana',
            'httpx',
            'aiohttp',
            'pandas',
            'numpy',
            'yaml',
            'dotenv',
            'zmq',
            'loguru'
        ]
        
        for dep in core_deps:
            try:
                module = importlib.import_module(dep.replace('-', '_'))
                version = getattr(module, '__version__', 'unknown')
                print(f"✅ {dep}: {version}")
            except ImportError as e:
                print(f"⚠️ Optional dependency {dep} not available: {e}")
    
    def test_solana_dependencies(self):
        """Test Solana-specific dependencies."""
        solana_deps = [
            ('solders', '0.20.0'),
            ('solana', '0.29.0'),
            ('anchorpy', '0.17.0'),
            ('base58', '2.1.0')
        ]
        
        for dep, min_version in solana_deps:
            try:
                module = importlib.import_module(dep)
                version = getattr(module, '__version__', 'unknown')
                print(f"✅ {dep}: {version} (min: {min_version})")
            except ImportError as e:
                pytest.fail(f"❌ Failed to import Solana dependency {dep}: {e}")
    
    def test_visualization_dependencies(self):
        """Test visualization and UI dependencies."""
        viz_deps = [
            'streamlit',
            'plotly',
            'matplotlib',
            'rich'
        ]
        
        for dep in viz_deps:
            try:
                module = importlib.import_module(dep)
                version = getattr(module, '__version__', 'unknown')
                print(f"✅ {dep}: {version}")
            except ImportError as e:
                pytest.fail(f"❌ Failed to import visualization dependency {dep}: {e}")
    
    def test_data_processing_dependencies(self):
        """Test data processing dependencies."""
        data_deps = [
            'pandas',
            'numpy',
            'scipy',
            'sklearn',
            'ta'
        ]
        
        for dep in data_deps:
            try:
                module = importlib.import_module(dep.replace('-', '_'))
                version = getattr(module, '__version__', 'unknown')
                print(f"✅ {dep}: {version}")
            except ImportError as e:
                print(f"⚠️ Optional data processing dependency {dep} not available: {e}")
    
    def test_monitoring_dependencies(self):
        """Test monitoring and alerting dependencies."""
        monitoring_deps = [
            'prometheus_client',
            'psutil'
        ]
        
        for dep in monitoring_deps:
            try:
                module = importlib.import_module(dep)
                version = getattr(module, '__version__', 'unknown')
                print(f"✅ {dep}: {version}")
            except ImportError as e:
                pytest.fail(f"❌ Failed to import monitoring dependency {dep}: {e}")
    
    def test_development_dependencies(self):
        """Test development and testing dependencies."""
        dev_deps = [
            'pytest',
            'black',
            'mypy',
            'flake8'
        ]
        
        for dep in dev_deps:
            try:
                module = importlib.import_module(dep)
                version = getattr(module, '__version__', 'unknown')
                print(f"✅ {dep}: {version}")
            except ImportError as e:
                print(f"⚠️ Development dependency {dep} not available: {e}")
    
    def test_project_structure(self):
        """Test project directory structure."""
        required_dirs = [
            'core',
            'phase_4_deployment',
            'scripts',
            'tests',
            'config'
        ]
        
        for dir_name in required_dirs:
            dir_path = Path(dir_name)
            if dir_path.exists() and dir_path.is_dir():
                print(f"✅ Directory exists: {dir_name}")
            else:
                print(f"⚠️ Directory missing or not accessible: {dir_name}")
    
    def test_configuration_files(self):
        """Test configuration files exist."""
        config_files = [
            'requirements.txt',
            'config.yaml',
            'activate_synergy7.sh'
        ]
        
        for file_name in config_files:
            file_path = Path(file_name)
            if file_path.exists() and file_path.is_file():
                print(f"✅ File exists: {file_name}")
            else:
                print(f"⚠️ File missing or not accessible: {file_name}")

if __name__ == "__main__":
    # Run tests directly
    test_env = TestEnvironmentSetup()
    
    print("🔍 Running Environment Setup Tests...")
    print("=" * 60)
    
    try:
        test_env.test_python_version()
        test_env.test_virtual_environment()
        test_env.test_core_dependencies()
        test_env.test_solana_dependencies()
        test_env.test_visualization_dependencies()
        test_env.test_data_processing_dependencies()
        test_env.test_monitoring_dependencies()
        test_env.test_development_dependencies()
        test_env.test_project_structure()
        test_env.test_configuration_files()
        
        print("=" * 60)
        print("🎉 All environment tests passed!")
        
    except Exception as e:
        print("=" * 60)
        print(f"❌ Environment test failed: {e}")
        sys.exit(1)
