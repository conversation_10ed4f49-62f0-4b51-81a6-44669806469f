# Synergy7 Trading System - System Tests

This directory contains comprehensive system tests for the Synergy7 Trading System. These tests verify that all components are properly installed, configured, and functioning correctly.

## 📋 Test Modules

### 1. Environment Setup Tests (`test_environment_setup.py`)
- ✅ Python version compatibility
- ✅ Virtual environment activation
- ✅ Core dependencies installation
- ✅ Solana dependencies verification
- ✅ Visualization libraries
- ✅ Data processing libraries
- ✅ Monitoring and alerting tools
- ✅ Development tools
- ✅ Project structure validation
- ✅ Configuration files existence

### 2. Core Imports Tests (`test_core_imports.py`)
- ✅ Solana-related imports (solders, solana, anchorpy)
- ✅ Data processing imports (pandas, numpy, scipy)
- ✅ Networking imports (httpx, aiohttp, websockets)
- ✅ Visualization imports (streamlit, plotly, matplotlib, rich)
- ✅ Configuration imports (yaml, dotenv)
- ✅ Monitoring imports (psutil, prometheus_client)
- ✅ Project module imports
- ✅ Basic functionality tests
- ✅ Async functionality tests

### 3. Trading Components Tests (`test_trading_components.py`)
- ✅ Solana transaction creation
- ✅ Wallet operations
- ✅ HTTP client functionality
- ✅ Async HTTP client
- ✅ Data processing functionality
- ✅ Configuration loading
- ✅ Monitoring functionality
- ✅ Visualization functionality
- ✅ Telegram functionality (optional)

### 4. Performance Tests (`test_performance.py`)
- ✅ Import performance benchmarks
- ✅ Memory usage monitoring
- ✅ CPU performance tests
- ✅ Async operation performance
- ✅ HTTP client performance
- ✅ Solana operations performance
- ✅ Data processing performance
- ✅ System resource availability

## 🚀 Running Tests

### Run All Tests
```bash
# Activate environment first
source synergy7_env/bin/activate

# Run comprehensive test suite
python system_tests/run_all_tests.py
```

### Run Individual Test Modules
```bash
# Environment setup tests
python system_tests/test_environment_setup.py

# Core imports tests
python system_tests/test_core_imports.py

# Trading components tests
python system_tests/test_trading_components.py

# Performance tests
python system_tests/test_performance.py
```

### Run with Pytest
```bash
# Install pytest if not already installed
pip install pytest

# Run all tests with pytest
pytest system_tests/

# Run specific test file
pytest system_tests/test_environment_setup.py -v

# Run with coverage
pytest system_tests/ --cov=. --cov-report=html
```

## 📊 Test Reports

The master test runner (`run_all_tests.py`) generates comprehensive reports:

- **Console Output**: Real-time test progress and results
- **File Reports**: Saved to `system_tests/reports/test_report_YYYYMMDD_HHMMSS.txt`
- **System Information**: Hardware and software environment details
- **Performance Metrics**: Timing and resource usage data

### Sample Report Structure
```
📊 SYNERGY7 TRADING SYSTEM TEST REPORT
================================================================================
📅 Test run completed: 2024-01-15 14:30:45
⏱️ Total duration: 45.67 seconds
📈 Tests run: 4
✅ Passed: 4
❌ Failed: 0
⚠️ Warnings: 0
📊 Success rate: 100.0%

📋 DETAILED RESULTS
================================================================================
✅ test_environment_setup: PASSED (8.23s)
✅ test_core_imports: PASSED (12.45s)
✅ test_trading_components: PASSED (15.67s)
✅ test_performance: PASSED (9.32s)

💻 SYSTEM INFORMATION
================================================================================
🖥️ Platform: macOS-14.0-arm64
🐍 Python: 3.9.6
💾 Memory: 16.0 GB
🔧 CPU cores: 8
💽 Disk free: 245.3 GB

🎉 ALL TESTS PASSED! System is ready for trading.
✅ Synergy7 Trading System is fully operational.
```

## 🔧 Configuration

Test configuration is defined in `system_tests/__init__.py`:

```python
TEST_CONFIG = {
    'timeout': 300,  # 5 minutes default timeout
    'performance_thresholds': {
        'import_time': 5.0,  # seconds
        'memory_limit': 500,  # MB
        'cpu_time': 10.0,    # seconds
    },
    'required_dependencies': [
        'solders', 'solana', 'httpx', 'aiohttp',
        'pandas', 'numpy', 'rich', 'streamlit', 'plotly'
    ]
}
```

## 🎯 Performance Thresholds

The tests include performance benchmarks with the following thresholds:

- **Import Time**: < 5 seconds per module
- **Memory Usage**: < 500 MB for test operations
- **CPU Operations**: < 10 seconds for computational tests
- **Async Operations**: < 2 seconds for 100 concurrent operations
- **HTTP Client Creation**: < 1 second for 10 clients
- **Keypair Generation**: < 5 seconds for 100 keypairs

## 🐛 Troubleshooting

### Common Issues

1. **Import Errors**
   ```bash
   # Ensure virtual environment is activated
   source synergy7_env/bin/activate
   
   # Reinstall dependencies
   pip install -r requirements.txt
   ```

2. **Performance Test Failures**
   ```bash
   # Check system resources
   python -c "import psutil; print(f'Memory: {psutil.virtual_memory().available/1024**3:.1f}GB')"
   
   # Close other applications to free resources
   ```

3. **Solana Test Failures**
   ```bash
   # Check solders version
   python -c "import solders; print(solders.__version__)"
   
   # Reinstall solders if needed
   pip install --upgrade "solders>=0.20.0,<0.21.0"
   ```

### Getting Help

If tests fail:

1. **Check the detailed error output** in the console
2. **Review the generated report file** in `system_tests/reports/`
3. **Run individual test modules** to isolate issues
4. **Verify environment setup** with `./activate_synergy7.sh`
5. **Check system resources** (memory, disk space, CPU)

## ✅ Success Criteria

The system is considered ready for trading when:

- ✅ All environment tests pass
- ✅ All core imports work correctly
- ✅ All trading components function properly
- ✅ Performance meets established thresholds
- ✅ No critical errors or failures
- ✅ System resources are adequate

## 📈 Continuous Integration

These tests can be integrated into CI/CD pipelines:

```yaml
# Example GitHub Actions workflow
- name: Run System Tests
  run: |
    source synergy7_env/bin/activate
    python system_tests/run_all_tests.py
```

The tests return appropriate exit codes:
- **0**: All tests passed
- **1**: One or more tests failed
