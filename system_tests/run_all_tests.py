#!/usr/bin/env python3
"""
Master Test Runner for Synergy7 Trading System
Runs all system tests and generates a comprehensive report.
"""

import sys
import os
import time
import subprocess
from pathlib import Path
from datetime import datetime
import traceback

# Add project root to path
project_root = Path(__file__).parent.parent
sys.path.append(str(project_root))

class SystemTestRunner:
    """Master test runner for all system tests."""
    
    def __init__(self):
        self.test_results = {}
        self.start_time = None
        self.end_time = None
        self.total_tests = 0
        self.passed_tests = 0
        self.failed_tests = 0
        self.warnings = []
    
    def run_test_module(self, module_name, description):
        """Run a single test module."""
        print(f"\n{'='*60}")
        print(f"🔍 Running {description}")
        print(f"{'='*60}")
        
        module_path = Path(__file__).parent / f"{module_name}.py"
        
        if not module_path.exists():
            print(f"❌ Test module not found: {module_path}")
            self.test_results[module_name] = {
                'status': 'FAILED',
                'error': 'Module not found',
                'duration': 0
            }
            self.failed_tests += 1
            return False
        
        start_time = time.time()
        
        try:
            # Import and run the test module
            module = __import__(module_name)
            
            # Check if module has main function
            if hasattr(module, '__main__') or module_name in sys.modules:
                # Run the module directly
                result = subprocess.run([
                    sys.executable, str(module_path)
                ], capture_output=True, text=True, cwd=project_root)
                
                duration = time.time() - start_time
                
                if result.returncode == 0:
                    print(f"✅ {description} - PASSED ({duration:.2f}s)")
                    self.test_results[module_name] = {
                        'status': 'PASSED',
                        'duration': duration,
                        'output': result.stdout
                    }
                    self.passed_tests += 1
                    return True
                else:
                    print(f"❌ {description} - FAILED ({duration:.2f}s)")
                    print(f"Error output: {result.stderr}")
                    self.test_results[module_name] = {
                        'status': 'FAILED',
                        'duration': duration,
                        'error': result.stderr,
                        'output': result.stdout
                    }
                    self.failed_tests += 1
                    return False
            else:
                print(f"⚠️ {description} - No main function found")
                self.warnings.append(f"{module_name}: No main function")
                return True
                
        except Exception as e:
            duration = time.time() - start_time
            error_msg = f"Exception: {str(e)}\n{traceback.format_exc()}"
            print(f"❌ {description} - FAILED ({duration:.2f}s)")
            print(f"Error: {error_msg}")
            
            self.test_results[module_name] = {
                'status': 'FAILED',
                'duration': duration,
                'error': error_msg
            }
            self.failed_tests += 1
            return False
    
    def run_all_tests(self):
        """Run all system tests."""
        self.start_time = datetime.now()
        
        print("🚀 Starting Synergy7 Trading System Tests")
        print(f"📅 Start time: {self.start_time.strftime('%Y-%m-%d %H:%M:%S')}")
        print(f"🐍 Python version: {sys.version}")
        print(f"📁 Working directory: {os.getcwd()}")
        
        # Define test modules to run
        test_modules = [
            ('test_environment_setup', 'Environment Setup Tests'),
            ('test_core_imports', 'Core Imports Tests'),
            ('test_trading_components', 'Trading Components Tests'),
            ('test_performance', 'Performance Tests')
        ]
        
        self.total_tests = len(test_modules)
        
        # Run each test module
        for module_name, description in test_modules:
            self.run_test_module(module_name, description)
        
        self.end_time = datetime.now()
        
        # Generate final report
        self.generate_report()
    
    def generate_report(self):
        """Generate comprehensive test report."""
        duration = self.end_time - self.start_time
        
        print(f"\n{'='*80}")
        print("📊 SYNERGY7 TRADING SYSTEM TEST REPORT")
        print(f"{'='*80}")
        
        print(f"📅 Test run completed: {self.end_time.strftime('%Y-%m-%d %H:%M:%S')}")
        print(f"⏱️ Total duration: {duration.total_seconds():.2f} seconds")
        print(f"📈 Tests run: {self.total_tests}")
        print(f"✅ Passed: {self.passed_tests}")
        print(f"❌ Failed: {self.failed_tests}")
        print(f"⚠️ Warnings: {len(self.warnings)}")
        
        # Success rate
        if self.total_tests > 0:
            success_rate = (self.passed_tests / self.total_tests) * 100
            print(f"📊 Success rate: {success_rate:.1f}%")
        
        # Detailed results
        print(f"\n{'='*60}")
        print("📋 DETAILED RESULTS")
        print(f"{'='*60}")
        
        for module_name, result in self.test_results.items():
            status_icon = "✅" if result['status'] == 'PASSED' else "❌"
            duration = result.get('duration', 0)
            print(f"{status_icon} {module_name}: {result['status']} ({duration:.2f}s)")
            
            if result['status'] == 'FAILED' and 'error' in result:
                print(f"   Error: {result['error'][:200]}...")
        
        # Warnings
        if self.warnings:
            print(f"\n{'='*60}")
            print("⚠️ WARNINGS")
            print(f"{'='*60}")
            for warning in self.warnings:
                print(f"⚠️ {warning}")
        
        # System information
        print(f"\n{'='*60}")
        print("💻 SYSTEM INFORMATION")
        print(f"{'='*60}")
        
        try:
            import psutil
            import platform
            
            print(f"🖥️ Platform: {platform.platform()}")
            print(f"🐍 Python: {platform.python_version()}")
            print(f"💾 Memory: {psutil.virtual_memory().total / (1024**3):.1f} GB")
            print(f"🔧 CPU cores: {psutil.cpu_count()}")
            print(f"💽 Disk free: {psutil.disk_usage('/').free / (1024**3):.1f} GB")
            
        except ImportError:
            print("⚠️ System information not available (psutil not installed)")
        
        # Final status
        print(f"\n{'='*80}")
        if self.failed_tests == 0:
            print("🎉 ALL TESTS PASSED! System is ready for trading.")
            print("✅ Synergy7 Trading System is fully operational.")
        else:
            print(f"❌ {self.failed_tests} TEST(S) FAILED!")
            print("⚠️ Please fix the issues before using the trading system.")
        print(f"{'='*80}")
        
        # Save report to file
        self.save_report_to_file()
        
        return self.failed_tests == 0
    
    def save_report_to_file(self):
        """Save test report to file."""
        try:
            report_dir = Path("system_tests/reports")
            report_dir.mkdir(exist_ok=True)
            
            timestamp = self.start_time.strftime('%Y%m%d_%H%M%S')
            report_file = report_dir / f"test_report_{timestamp}.txt"
            
            with open(report_file, 'w') as f:
                f.write(f"Synergy7 Trading System Test Report\n")
                f.write(f"Generated: {self.end_time.strftime('%Y-%m-%d %H:%M:%S')}\n")
                f.write(f"Duration: {(self.end_time - self.start_time).total_seconds():.2f}s\n")
                f.write(f"Tests: {self.total_tests}, Passed: {self.passed_tests}, Failed: {self.failed_tests}\n\n")
                
                for module_name, result in self.test_results.items():
                    f.write(f"{module_name}: {result['status']} ({result.get('duration', 0):.2f}s)\n")
                    if result['status'] == 'FAILED' and 'error' in result:
                        f.write(f"  Error: {result['error']}\n")
                    f.write("\n")
            
            print(f"📄 Report saved to: {report_file}")
            
        except Exception as e:
            print(f"⚠️ Could not save report to file: {e}")

def main():
    """Main function to run all tests."""
    runner = SystemTestRunner()
    success = runner.run_all_tests()
    
    # Exit with appropriate code
    sys.exit(0 if success else 1)

if __name__ == "__main__":
    main()
