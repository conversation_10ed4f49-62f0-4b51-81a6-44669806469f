#!/usr/bin/env python3
"""
Performance Tests for Synergy7 Trading System
Tests system performance and resource usage.
"""

import sys
import os
import time
import asyncio
from pathlib import Path
import pytest
import psutil
from concurrent.futures import ThreadPoolExecutor

# Add project root to path
project_root = Path(__file__).parent.parent
sys.path.append(str(project_root))

class TestPerformance:
    """Test system performance and resource usage."""
    
    def test_import_performance(self):
        """Test import performance of key modules."""
        print("🔍 Testing import performance...")
        
        import_tests = [
            ('solders', 'from solders.keypair import Keypair'),
            ('pandas', 'import pandas as pd'),
            ('numpy', 'import numpy as np'),
            ('httpx', 'import httpx'),
            ('rich', 'from rich.console import Console'),
            ('streamlit', 'import streamlit as st')
        ]
        
        for module_name, import_statement in import_tests:
            start_time = time.time()
            try:
                exec(import_statement)
                import_time = time.time() - start_time
                print(f"✅ {module_name}: {import_time:.3f}s")
                
                # Assert reasonable import time (< 5 seconds)
                assert import_time < 5.0, f"{module_name} import too slow: {import_time:.3f}s"
                
            except ImportError as e:
                print(f"⚠️ {module_name}: Import failed - {e}")
            except Exception as e:
                pytest.fail(f"❌ {module_name}: Import error - {e}")
    
    def test_memory_usage(self):
        """Test memory usage of key operations."""
        print("🔍 Testing memory usage...")
        
        process = psutil.Process()
        initial_memory = process.memory_info().rss / 1024 / 1024  # MB
        
        # Test pandas DataFrame creation
        import pandas as pd
        import numpy as np
        
        # Create large DataFrame
        data = np.random.random((10000, 10))
        df = pd.DataFrame(data)
        
        after_pandas = process.memory_info().rss / 1024 / 1024  # MB
        pandas_usage = after_pandas - initial_memory
        
        print(f"✅ Initial memory: {initial_memory:.1f} MB")
        print(f"✅ After pandas operations: {after_pandas:.1f} MB")
        print(f"✅ Pandas memory usage: {pandas_usage:.1f} MB")
        
        # Assert reasonable memory usage (< 500 MB for test)
        assert after_pandas < 500, f"Memory usage too high: {after_pandas:.1f} MB"
        
        # Clean up
        del df, data
    
    def test_cpu_performance(self):
        """Test CPU performance of key operations."""
        print("🔍 Testing CPU performance...")
        
        import numpy as np
        
        # Test numpy operations
        start_time = time.time()
        
        # Matrix operations
        a = np.random.random((1000, 1000))
        b = np.random.random((1000, 1000))
        c = np.dot(a, b)
        
        numpy_time = time.time() - start_time
        print(f"✅ NumPy matrix multiplication (1000x1000): {numpy_time:.3f}s")
        
        # Assert reasonable performance (< 10 seconds)
        assert numpy_time < 10.0, f"NumPy operations too slow: {numpy_time:.3f}s"
        
        # Test pandas operations
        import pandas as pd
        
        start_time = time.time()
        
        # DataFrame operations
        df = pd.DataFrame(np.random.random((100000, 10)))
        df['mean'] = df.mean(axis=1)
        df_grouped = df.groupby(df.index % 100).sum()
        
        pandas_time = time.time() - start_time
        print(f"✅ Pandas operations (100k rows): {pandas_time:.3f}s")
        
        # Assert reasonable performance (< 5 seconds)
        assert pandas_time < 5.0, f"Pandas operations too slow: {pandas_time:.3f}s"
    
    def test_async_performance(self):
        """Test async operation performance."""
        print("🔍 Testing async performance...")
        
        async def async_operation():
            await asyncio.sleep(0.01)  # Simulate async work
            return "done"
        
        async def test_concurrent_operations():
            start_time = time.time()
            
            # Run 100 concurrent operations
            tasks = [async_operation() for _ in range(100)]
            results = await asyncio.gather(*tasks)
            
            async_time = time.time() - start_time
            print(f"✅ 100 concurrent async operations: {async_time:.3f}s")
            
            # Should be much faster than sequential (< 2 seconds)
            assert async_time < 2.0, f"Async operations too slow: {async_time:.3f}s"
            assert len(results) == 100
            
            return async_time
        
        # Run async test
        try:
            asyncio.run(test_concurrent_operations())
        except Exception as e:
            pytest.fail(f"❌ Async performance test failed: {e}")
    
    def test_http_client_performance(self):
        """Test HTTP client performance."""
        print("🔍 Testing HTTP client performance...")
        
        import httpx
        
        # Test client creation performance
        start_time = time.time()
        
        clients = []
        for _ in range(10):
            client = httpx.Client()
            clients.append(client)
        
        creation_time = time.time() - start_time
        print(f"✅ HTTP client creation (10 clients): {creation_time:.3f}s")
        
        # Clean up
        for client in clients:
            client.close()
        
        # Assert reasonable performance (< 1 second)
        assert creation_time < 1.0, f"HTTP client creation too slow: {creation_time:.3f}s"
    
    def test_solana_operations_performance(self):
        """Test Solana operations performance."""
        print("🔍 Testing Solana operations performance...")
        
        try:
            from solders.keypair import Keypair
            from solders.transaction import Transaction
            from solders.instruction import Instruction
            from solders.pubkey import Pubkey
            
            # Test keypair generation performance
            start_time = time.time()
            
            keypairs = []
            for _ in range(100):
                keypair = Keypair()
                keypairs.append(keypair)
            
            keypair_time = time.time() - start_time
            print(f"✅ Keypair generation (100 keypairs): {keypair_time:.3f}s")
            
            # Assert reasonable performance (< 5 seconds)
            assert keypair_time < 5.0, f"Keypair generation too slow: {keypair_time:.3f}s"
            
        except Exception as e:
            pytest.fail(f"❌ Solana operations performance test failed: {e}")
    
    def test_data_processing_performance(self):
        """Test data processing performance."""
        print("🔍 Testing data processing performance...")
        
        import pandas as pd
        import numpy as np
        
        # Create large dataset
        n_rows = 50000
        data = {
            'timestamp': pd.date_range('2024-01-01', periods=n_rows, freq='1s'),
            'price': np.random.uniform(100, 200, n_rows),
            'volume': np.random.uniform(1000, 10000, n_rows)
        }
        df = pd.DataFrame(data)
        
        # Test various operations
        start_time = time.time()
        
        # Moving averages
        df['sma_10'] = df['price'].rolling(window=10).mean()
        df['sma_50'] = df['price'].rolling(window=50).mean()
        
        # Statistical operations
        df['price_std'] = df['price'].rolling(window=20).std()
        df['volume_mean'] = df['volume'].rolling(window=10).mean()
        
        # Groupby operations
        df['hour'] = df['timestamp'].dt.hour
        hourly_stats = df.groupby('hour').agg({
            'price': ['mean', 'std'],
            'volume': ['sum', 'mean']
        })
        
        processing_time = time.time() - start_time
        print(f"✅ Data processing ({n_rows} rows): {processing_time:.3f}s")
        
        # Assert reasonable performance (< 10 seconds)
        assert processing_time < 10.0, f"Data processing too slow: {processing_time:.3f}s"
    
    def test_system_resources(self):
        """Test system resource availability."""
        print("🔍 Testing system resources...")
        
        # Check CPU count
        cpu_count = psutil.cpu_count()
        print(f"✅ CPU cores: {cpu_count}")
        assert cpu_count >= 1
        
        # Check memory
        memory = psutil.virtual_memory()
        memory_gb = memory.total / (1024**3)
        print(f"✅ Total memory: {memory_gb:.1f} GB")
        print(f"✅ Available memory: {memory.available / (1024**3):.1f} GB")
        
        # Assert minimum memory (2 GB)
        assert memory_gb >= 2.0, f"Insufficient memory: {memory_gb:.1f} GB"
        
        # Check disk space
        disk = psutil.disk_usage('/')
        disk_gb = disk.total / (1024**3)
        free_gb = disk.free / (1024**3)
        print(f"✅ Total disk: {disk_gb:.1f} GB")
        print(f"✅ Free disk: {free_gb:.1f} GB")
        
        # Assert minimum free space (1 GB)
        assert free_gb >= 1.0, f"Insufficient disk space: {free_gb:.1f} GB"

if __name__ == "__main__":
    # Run tests directly
    test_perf = TestPerformance()
    
    print("🔍 Running Performance Tests...")
    print("=" * 60)
    
    try:
        test_perf.test_import_performance()
        test_perf.test_memory_usage()
        test_perf.test_cpu_performance()
        test_perf.test_async_performance()
        test_perf.test_http_client_performance()
        test_perf.test_solana_operations_performance()
        test_perf.test_data_processing_performance()
        test_perf.test_system_resources()
        
        print("=" * 60)
        print("🎉 All performance tests passed!")
        
    except Exception as e:
        print("=" * 60)
        print(f"❌ Performance test failed: {e}")
        sys.exit(1)
