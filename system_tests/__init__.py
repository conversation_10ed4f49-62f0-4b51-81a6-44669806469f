"""
Synergy7 Trading System - System Tests Package

This package contains comprehensive system tests for the Synergy7 Trading System.

Test Modules:
- test_environment_setup.py: Tests environment configuration and dependencies
- test_core_imports.py: Tests all critical module imports
- test_trading_components.py: Tests trading-specific functionality
- test_performance.py: Tests system performance and resource usage
- run_all_tests.py: Master test runner

Usage:
    # Run all tests
    python system_tests/run_all_tests.py
    
    # Run individual test modules
    python system_tests/test_environment_setup.py
    python system_tests/test_core_imports.py
    python system_tests/test_trading_components.py
    python system_tests/test_performance.py
    
    # Run with pytest
    pytest system_tests/
"""

__version__ = "1.0.0"
__author__ = "Synergy7 Trading System"

# Test configuration
TEST_CONFIG = {
    'timeout': 300,  # 5 minutes default timeout
    'performance_thresholds': {
        'import_time': 5.0,  # seconds
        'memory_limit': 500,  # MB
        'cpu_time': 10.0,    # seconds
    },
    'required_dependencies': [
        'solders',
        'solana',
        'httpx',
        'aiohttp',
        'pandas',
        'numpy',
        'rich',
        'streamlit',
        'plotly'
    ]
}
