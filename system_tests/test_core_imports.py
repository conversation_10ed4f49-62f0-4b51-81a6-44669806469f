#!/usr/bin/env python3
"""
Core Imports Tests for Synergy7 Trading System
Tests all critical module imports and basic functionality.
"""

import sys
import os
from pathlib import Path
import pytest

# Add project root to path
project_root = Path(__file__).parent.parent
sys.path.append(str(project_root))

class TestCoreImports:
    """Test core system imports and basic functionality."""
    
    def test_solana_imports(self):
        """Test Solana-related imports."""
        print("🔍 Testing Solana imports...")
        
        # Test solders imports
        try:
            from solders.keypair import Keypair
            from solders.transaction import Transaction
            from solders.pubkey import Pubkey
            print("✅ Solders imports successful")
        except ImportError as e:
            pytest.fail(f"❌ Solders import failed: {e}")
        
        # Test solana imports
        try:
            from solana.rpc.async_api import AsyncClient
            from solana.rpc.api import Client
            print("✅ Solana RPC imports successful")
        except ImportError as e:
            pytest.fail(f"❌ Solana RPC import failed: {e}")
        
        # Test anchorpy imports
        try:
            import anchorpy
            print("✅ AnchorPy imports successful")
        except ImportError as e:
            print(f"⚠️ AnchorPy import failed: {e}")
    
    def test_data_processing_imports(self):
        """Test data processing imports."""
        print("🔍 Testing data processing imports...")
        
        try:
            import pandas as pd
            import numpy as np
            import scipy
            print("✅ Core data processing imports successful")
        except ImportError as e:
            pytest.fail(f"❌ Data processing import failed: {e}")
        
        try:
            import ta
            print("✅ Technical analysis imports successful")
        except ImportError as e:
            print(f"⚠️ Technical analysis import failed: {e}")
        
        try:
            import vectorbt as vbt
            print("✅ VectorBT imports successful")
        except ImportError as e:
            print(f"⚠️ VectorBT import failed: {e}")
    
    def test_networking_imports(self):
        """Test networking and HTTP imports."""
        print("🔍 Testing networking imports...")
        
        try:
            import httpx
            import aiohttp
            import websockets
            import requests
            print("✅ Networking imports successful")
        except ImportError as e:
            pytest.fail(f"❌ Networking import failed: {e}")
    
    def test_visualization_imports(self):
        """Test visualization imports."""
        print("🔍 Testing visualization imports...")
        
        try:
            import streamlit as st
            import plotly.graph_objects as go
            import matplotlib.pyplot as plt
            import rich
            print("✅ Visualization imports successful")
        except ImportError as e:
            pytest.fail(f"❌ Visualization import failed: {e}")
    
    def test_configuration_imports(self):
        """Test configuration and utility imports."""
        print("🔍 Testing configuration imports...")
        
        try:
            import yaml
            import json
            import os
            from dotenv import load_dotenv
            import base58
            print("✅ Configuration imports successful")
        except ImportError as e:
            pytest.fail(f"❌ Configuration import failed: {e}")
    
    def test_monitoring_imports(self):
        """Test monitoring and alerting imports."""
        print("🔍 Testing monitoring imports...")
        
        try:
            import psutil
            import prometheus_client
            print("✅ Monitoring imports successful")
        except ImportError as e:
            pytest.fail(f"❌ Monitoring import failed: {e}")
        
        try:
            import telegram
            print("✅ Telegram imports successful")
        except ImportError as e:
            print(f"⚠️ Telegram import failed: {e}")
    
    def test_project_module_imports(self):
        """Test project-specific module imports."""
        print("🔍 Testing project module imports...")
        
        # Test core modules
        core_modules = [
            'core',
            'phase_4_deployment'
        ]
        
        for module in core_modules:
            try:
                __import__(module)
                print(f"✅ {module} module importable")
            except ImportError as e:
                print(f"⚠️ {module} module import failed: {e}")
    
    def test_basic_functionality(self):
        """Test basic functionality of key components."""
        print("🔍 Testing basic functionality...")
        
        # Test Solana keypair generation
        try:
            from solders.keypair import Keypair
            keypair = Keypair()
            assert keypair.pubkey() is not None
            print("✅ Solana keypair generation works")
        except Exception as e:
            pytest.fail(f"❌ Solana keypair generation failed: {e}")
        
        # Test pandas DataFrame creation
        try:
            import pandas as pd
            import numpy as np
            df = pd.DataFrame({'test': [1, 2, 3]})
            assert len(df) == 3
            print("✅ Pandas DataFrame creation works")
        except Exception as e:
            pytest.fail(f"❌ Pandas DataFrame creation failed: {e}")
        
        # Test HTTP client creation
        try:
            import httpx
            client = httpx.Client()
            client.close()
            print("✅ HTTP client creation works")
        except Exception as e:
            pytest.fail(f"❌ HTTP client creation failed: {e}")
        
        # Test rich console
        try:
            from rich.console import Console
            console = Console()
            print("✅ Rich console creation works")
        except Exception as e:
            pytest.fail(f"❌ Rich console creation failed: {e}")
    
    def test_async_functionality(self):
        """Test async functionality."""
        print("🔍 Testing async functionality...")
        
        import asyncio
        
        async def test_async():
            try:
                import aiohttp
                async with aiohttp.ClientSession() as session:
                    pass
                print("✅ Async HTTP client works")
            except Exception as e:
                pytest.fail(f"❌ Async HTTP client failed: {e}")
        
        # Run async test
        try:
            asyncio.run(test_async())
        except Exception as e:
            pytest.fail(f"❌ Async functionality test failed: {e}")

if __name__ == "__main__":
    # Run tests directly
    test_imports = TestCoreImports()
    
    print("🔍 Running Core Imports Tests...")
    print("=" * 60)
    
    try:
        test_imports.test_solana_imports()
        test_imports.test_data_processing_imports()
        test_imports.test_networking_imports()
        test_imports.test_visualization_imports()
        test_imports.test_configuration_imports()
        test_imports.test_monitoring_imports()
        test_imports.test_project_module_imports()
        test_imports.test_basic_functionality()
        test_imports.test_async_functionality()
        
        print("=" * 60)
        print("🎉 All core import tests passed!")
        
    except Exception as e:
        print("=" * 60)
        print(f"❌ Core import test failed: {e}")
        sys.exit(1)
