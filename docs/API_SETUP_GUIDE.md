# 🔑 Synergy7 Trading System - API Setup Guide

This guide explains all the APIs needed for the Synergy7 Trading System and how to obtain the required API keys.

## 📋 Quick Setup Checklist

### ✅ **REQUIRED APIs** (System won't work without these)
- [ ] **Helius API** - Solana RPC endpoint
- [ ] **Wallet Setup** - Solana wallet with private key
- [ ] **Telegram Bot** - For trade notifications
- [ ] **Birdeye API** - Token price data

### 🚀 **RECOMMENDED APIs** (For enhanced performance)
- [ ] **QuickNode API** - Premium Solana RPC
- [ ] **CoinGecko API** - Additional price data

### ⭐ **OPTIONAL APIs** (For advanced features)
- [ ] **Jito MEV Protection** - MEV-resistant transactions
- [ ] **Prometheus/Grafana** - System monitoring

---

## 🌐 **SOLANA RPC ENDPOINTS**

### 1. **Helius API** (REQUIRED)
**Purpose**: Primary Solana RPC endpoint for blockchain interactions

**How to get**:
1. Visit [helius.dev](https://helius.dev)
2. Sign up for a free account
3. Create a new project
4. Copy your API key from the dashboard

**Free Tier**: 100,000 requests/month
**Pricing**: $99/month for 1M requests

**Environment Variables**:
```bash
HELIUS_API_KEY=your_helius_api_key_here
HELIUS_RPC_URL=https://mainnet.helius-rpc.com/?api-key=${HELIUS_API_KEY}
```

### 2. **QuickNode API** (RECOMMENDED)
**Purpose**: Premium Solana RPC with advanced features (bundles, streaming)

**How to get**:
1. Visit [quicknode.com](https://quicknode.com)
2. Sign up and create a Solana Mainnet endpoint
3. Enable additional services (Bundles, Price Feeds, Streaming)
4. Copy your endpoint URL and API key

**Free Tier**: Limited requests
**Pricing**: $9-299/month depending on usage

**Environment Variables**:
```bash
QUICKNODE_API_KEY=your_quicknode_api_key_here
QUICKNODE_RPC_URL=https://your-endpoint.solana-mainnet.quiknode.pro/your_token/
```

---

## 📊 **MARKET DATA APIs**

### 3. **Birdeye API** (REQUIRED)
**Purpose**: Real-time token prices, market data, and DEX analytics

**How to get**:
1. Visit [birdeye.so](https://birdeye.so)
2. Go to API section and sign up
3. Choose a plan (free tier available)
4. Copy your API key

**Free Tier**: 1,000 requests/day
**Pricing**: $49-499/month

**Environment Variables**:
```bash
BIRDEYE_API_KEY=your_birdeye_api_key_here
```

### 4. **CoinGecko API** (RECOMMENDED)
**Purpose**: Additional price data and market information

**How to get**:
1. Visit [coingecko.com/api](https://coingecko.com/api)
2. Sign up for API access
3. Choose a plan (free tier available)
4. Copy your API key

**Free Tier**: 10,000 requests/month
**Pricing**: $129-999/month

**Environment Variables**:
```bash
COINGECKO_API_KEY=your_coingecko_api_key_here
```

---

## 📱 **TELEGRAM NOTIFICATIONS**

### 5. **Telegram Bot** (REQUIRED)
**Purpose**: Real-time trade notifications and system alerts

**How to get**:
1. Open Telegram and search for `@BotFather`
2. Send `/newbot` command
3. Follow instructions to create your bot
4. Copy the bot token provided
5. Get your chat ID:
   - Send a message to your bot
   - Visit: `https://api.telegram.org/bot<YOUR_BOT_TOKEN>/getUpdates`
   - Find your chat ID in the response

**Cost**: Free

**Environment Variables**:
```bash
TELEGRAM_BOT_TOKEN=your_telegram_bot_token_here
TELEGRAM_CHAT_ID=your_telegram_chat_id_here
```

---

## 💰 **WALLET SETUP**

### 6. **Solana Wallet** (REQUIRED)
**Purpose**: Execute trades on Solana blockchain

**How to setup**:

**Option A: Create New Wallet**
```bash
# Run the wallet creation script
python create_native_wallet.py
```

**Option B: Use Existing Wallet**
1. Export private key from Phantom/Solflare
2. Convert to base58 format if needed

**⚠️ SECURITY WARNING**: 
- Never share your private key
- Use a dedicated trading wallet
- Start with small amounts for testing

**Environment Variables**:
```bash
WALLET_ADDRESS=your_wallet_public_key_here
WALLET_PRIVATE_KEY=your_base58_private_key_here
```

---

## 🚀 **ADVANCED SERVICES** (Optional)

### 7. **Jito MEV Protection**
**Purpose**: MEV-resistant transaction submission

**How to get**:
1. Visit [jito.wtf](https://jito.wtf)
2. No API key required for basic usage
3. For advanced features, contact Jito team

**Cost**: Transaction fees only

**Environment Variables**:
```bash
JITO_RPC_URL=https://mainnet.block-engine.jito.wtf/api/v1
```

### 8. **Monitoring Stack**
**Purpose**: System monitoring and alerting

**Prometheus**: Built-in metrics collection
**Grafana**: Dashboard visualization

**Setup**: Included in Docker deployment

**Environment Variables**:
```bash
PROMETHEUS_ENABLED=true
GRAFANA_ADMIN_PASSWORD=your_secure_password
```

---

## 💰 **COST BREAKDOWN**

### **Minimum Setup** (Required APIs)
- **Helius**: Free tier (100k requests/month)
- **Birdeye**: Free tier (1k requests/day)
- **Telegram**: Free
- **Total**: $0/month

### **Recommended Setup**
- **Helius**: $99/month (1M requests)
- **QuickNode**: $49/month (starter plan)
- **Birdeye**: $49/month (pro plan)
- **CoinGecko**: Free tier
- **Total**: ~$197/month

### **Professional Setup**
- **Helius**: $299/month (10M requests)
- **QuickNode**: $299/month (premium)
- **Birdeye**: $199/month (enterprise)
- **CoinGecko**: $129/month (analyst)
- **Total**: ~$926/month

---

## 🔧 **SETUP INSTRUCTIONS**

### Step 1: Copy Environment File
```bash
cp .env.example .env
```

### Step 2: Fill in Required APIs
Edit `.env` file with your API keys:
```bash
# Required
HELIUS_API_KEY=your_actual_key_here
BIRDEYE_API_KEY=your_actual_key_here
TELEGRAM_BOT_TOKEN=your_actual_token_here
TELEGRAM_CHAT_ID=your_actual_chat_id_here
WALLET_ADDRESS=your_actual_address_here
WALLET_PRIVATE_KEY=your_actual_key_here
```

### Step 3: Test Configuration
```bash
# Activate environment
source synergy7_env/bin/activate

# Test API connections
python system_tests/test_environment_setup.py

# Validate system
python scripts/validate_profitable_system.py
```

### Step 4: Start Trading
```bash
# Start with dry run first
DRY_RUN=true python scripts/unified_live_trading.py

# Go live when ready
python scripts/unified_live_trading.py
```

---

## 🔒 **SECURITY BEST PRACTICES**

1. **Never commit .env file** to version control
2. **Use dedicated trading wallet** with limited funds
3. **Rotate API keys regularly**
4. **Monitor API usage** to detect anomalies
5. **Use strong passwords** for all accounts
6. **Enable 2FA** where available
7. **Test with small amounts** first

---

## 🆘 **TROUBLESHOOTING**

### Common Issues:

**"API key invalid"**
- Double-check key is copied correctly
- Ensure no extra spaces or characters
- Verify key hasn't expired

**"Rate limit exceeded"**
- Upgrade to higher tier plan
- Implement request throttling
- Use multiple API providers

**"Wallet connection failed"**
- Verify private key format (base58)
- Check wallet has sufficient SOL for fees
- Ensure RPC endpoint is working

**"Telegram notifications not working"**
- Verify bot token is correct
- Check chat ID is accurate
- Ensure bot is started (send /start)

---

## 📞 **SUPPORT**

For additional help:
1. Check the troubleshooting section above
2. Review system logs in `logs/` directory
3. Run diagnostic tests: `python system_tests/run_all_tests.py`
4. Check API provider documentation
5. Contact API providers for service-specific issues

---

**⚠️ Remember**: Start with the free tiers and small amounts to test everything works before scaling up!
